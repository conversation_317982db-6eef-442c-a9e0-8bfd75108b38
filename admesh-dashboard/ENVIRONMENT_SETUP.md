# AdMesh Dashboard Environment Setup Guide

This guide provides a streamlined approach to managing environments in the AdMesh Dashboard.

## 🌍 Available Environments

| Environment | API URL | Debug | Use Case |
|-------------|---------|-------|----------|
| **Development** | `http://127.0.0.1:8000` | ✅ Enabled | Local development with hot reload |
| **Test** | `http://127.0.0.1:8000` | ❌ Disabled | Testing production builds locally |
| **Production** | `https://api.useadmesh.com` | ❌ Disabled | Live deployment |

## 🚀 Quick Start Commands

### Development (Most Common)
```bash
npm run dev                    # Switch to dev environment and start dev server
```

### Testing Production Builds Locally
```bash
npm run test                   # Switch to test environment, build, and start
```

### Production Deployment
```bash
npm run prod                   # Switch to production environment, build, and start
```

## 🔧 Environment Management

### Switch Environments Only
```bash
npm run env:dev               # Switch to development environment
npm run env:test              # Switch to test environment
npm run env:prod              # Switch to production environment
```

### Validate Current Environment
```bash
npm run validate-env          # Check if current environment is properly configured
```

### Test Integration
```bash
npm run test-integration      # Run frontend-backend integration tests
```

## 📁 Environment Files

- **`.env.dev`** - Development configuration
- **`.env.test`** - Test configuration (production-like with local API)
- **`.env.production`** - Production configuration
- **`.env`** - Active environment (auto-generated, don't edit manually)

## 🔍 Environment Variables

### Common Variables
- `NEXT_PUBLIC_ENVIRONMENT` - Current environment name
- `NEXT_PUBLIC_API_BASE_URL` - Backend API URL
- `NEXT_PUBLIC_DEBUG` - Debug mode flag
- `NEXT_PUBLIC_LOG_LEVEL` - Logging level
- `NEXT_PUBLIC_FIREBASE_PROJECT_ID` - Firebase project ID

### Development Specific
- Debug enabled for detailed logging
- Uses `admesh-dev` Firebase project
- Hot reload enabled

### Test Specific
- Production-like settings but with local API
- Debug disabled for realistic testing
- Uses `admesh-dev` Firebase project

### Production Specific
- All debugging disabled
- Uses `admesh-9560c` Firebase project
- Optimized for performance

The test environment allows you to test production builds locally while still connecting to your local backend server.
