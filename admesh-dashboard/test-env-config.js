#!/usr/bin/env node

// Environment validation script
const fs = require('fs');
const path = require('path');

console.log('🔧 AdMesh Dashboard Environment Validation');
console.log('==========================================');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('❌ ERROR: .env file not found');
  console.log('   Run: npm run env:dev, npm run env:test, or npm run env:prod');
  process.exit(1);
}

// Read environment variables
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  if (line.includes('=') && !line.startsWith('#')) {
    const [key, value] = line.split('=', 2);
    envVars[key.trim()] = value.trim();
  }
});

console.log('Environment Variables:');
console.log('  NEXT_PUBLIC_ENVIRONMENT:', envVars.NEXT_PUBLIC_ENVIRONMENT);
console.log('  NEXT_PUBLIC_API_BASE_URL:', envVars.NEXT_PUBLIC_API_BASE_URL);
console.log('  NEXT_PUBLIC_DEBUG:', envVars.NEXT_PUBLIC_DEBUG);
console.log('  NEXT_PUBLIC_LOG_LEVEL:', envVars.NEXT_PUBLIC_LOG_LEVEL);
console.log('  NEXT_PUBLIC_FIREBASE_PROJECT_ID:', envVars.NEXT_PUBLIC_FIREBASE_PROJECT_ID);
console.log('');

// Validate environment configuration
const environment = envVars.NEXT_PUBLIC_ENVIRONMENT;
const apiUrl = envVars.NEXT_PUBLIC_API_BASE_URL;
const debug = envVars.NEXT_PUBLIC_DEBUG;

let isValid = true;
const errors = [];
const warnings = [];

// Environment-specific validations
if (environment === 'development') {
  if (apiUrl !== 'http://127.0.0.1:8000') {
    errors.push(`Development API URL should be http://127.0.0.1:8000, got: ${apiUrl}`);
    isValid = false;
  }
  if (debug !== 'true') {
    warnings.push(`Development debug should be true, got: ${debug}`);
  }
} else if (environment === 'test') {
  if (apiUrl !== 'http://127.0.0.1:8000') {
    errors.push(`Test API URL should be http://127.0.0.1:8000, got: ${apiUrl}`);
    isValid = false;
  }
  if (debug !== 'false') {
    warnings.push(`Test debug should be false, got: ${debug}`);
  }
} else if (environment === 'production') {
  if (apiUrl !== 'https://api.useadmesh.com') {
    errors.push(`Production API URL should be https://api.useadmesh.com, got: ${apiUrl}`);
    isValid = false;
  }
  if (debug !== 'false') {
    errors.push(`Production debug should be false, got: ${debug}`);
    isValid = false;
  }
} else {
  errors.push(`Invalid environment: ${environment}. Should be development, test, or production`);
  isValid = false;
}

// Display results
if (errors.length > 0) {
  console.log('❌ ERRORS:');
  errors.forEach(error => console.log(`   - ${error}`));
  console.log('');
}

if (warnings.length > 0) {
  console.log('⚠️  WARNINGS:');
  warnings.forEach(warning => console.log(`   - ${warning}`));
  console.log('');
}

if (isValid && errors.length === 0) {
  console.log('✅ Environment configuration is valid!');
  console.log(`🌍 Current environment: ${environment}`);
  console.log(`🔗 API URL: ${apiUrl}`);
} else {
  console.log('❌ Environment configuration has issues');
  process.exit(1);
}
