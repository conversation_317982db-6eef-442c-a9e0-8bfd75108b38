#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const args = process.argv.slice(2);
const environment = args[0];

// Environment configuration
const environments = {
  dev: {
    file: '.env.dev',
    name: 'Development',
    description: 'Local development with hot reload and debugging',
    apiUrl: 'http://127.0.0.1:8000',
    debug: true
  },
  test: {
    file: '.env.test',
    name: 'Test',
    description: 'Production build testing with local API',
    apiUrl: 'http://127.0.0.1:8000',
    debug: false
  },
  prod: {
    file: '.env.production',
    name: 'Production',
    description: 'Live deployment configuration',
    apiUrl: 'https://api.useadmesh.com',
    debug: false
  },
  production: {
    file: '.env.production',
    name: 'Production',
    description: 'Live deployment configuration',
    apiUrl: 'https://api.useadmesh.com',
    debug: false
  }
};

if (!environment || !environments[environment]) {
  console.log('❌ Please specify a valid environment');
  console.log('Usage: node scripts/switch-env.js [dev|test|prod]');
  console.log('');
  console.log('Available environments:');
  Object.entries(environments).forEach(([key, config]) => {
    if (key !== 'production') { // Don't show duplicate
      console.log(`  ${key.padEnd(4)} - ${config.name}: ${config.description}`);
    }
  });
  process.exit(1);
}

const config = environments[environment];
const envPath = path.join(__dirname, '..', config.file);
const targetPath = path.join(__dirname, '..', '.env');

if (!fs.existsSync(envPath)) {
  console.log(`❌ Environment file ${config.file} not found`);
  process.exit(1);
}

try {
  fs.copyFileSync(envPath, targetPath);

  // Read and validate the environment file
  const envContent = fs.readFileSync(targetPath, 'utf8');
  const actualApiUrl = envContent.match(/NEXT_PUBLIC_API_BASE_URL=(.+)/)?.[1];
  const actualEnv = envContent.match(/NEXT_PUBLIC_ENVIRONMENT=(.+)/)?.[1];
  const actualDebug = envContent.match(/NEXT_PUBLIC_DEBUG=(.+)/)?.[1];

  console.log('✅ Environment switched successfully!');
  console.log(`🌍 Environment: ${config.name} (${actualEnv})`);
  console.log(`🔗 API URL: ${actualApiUrl}`);
  console.log(`🐛 Debug Mode: ${actualDebug}`);
  console.log(`📝 Description: ${config.description}`);
  console.log('');

  // Validate configuration
  if (actualApiUrl !== config.apiUrl) {
    console.log(`⚠️  Warning: API URL mismatch. Expected: ${config.apiUrl}, Got: ${actualApiUrl}`);
  }

  console.log('Available commands:');
  console.log('  npm run dev         - Start development server');
  console.log('  npm run build       - Build for production');
  console.log('  npm run start       - Start production server');
  console.log('  npm run test        - Build and start test server');
  console.log('  npm run prod        - Build and start production server');
  console.log('  npm run validate-env - Validate current environment');
  console.log('');
  console.log('Environment switching:');
  console.log('  npm run env:dev     - Switch to development');
  console.log('  npm run env:test    - Switch to test');
  console.log('  npm run env:prod    - Switch to production');

} catch (error) {
  console.log('❌ Error switching environment:', error.message);
  process.exit(1);
}
