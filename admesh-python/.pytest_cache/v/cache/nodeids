["tests/api_resources/test_recommend.py::TestAsyncRecommend::test_method_get_recommendations[loose]", "tests/api_resources/test_recommend.py::TestAsyncRecommend::test_method_get_recommendations[strict]", "tests/api_resources/test_recommend.py::TestAsyncRecommend::test_method_get_recommendations_with_all_params[loose]", "tests/api_resources/test_recommend.py::TestAsyncRecommend::test_method_get_recommendations_with_all_params[strict]", "tests/api_resources/test_recommend.py::TestAsyncRecommend::test_raw_response_get_recommendations[loose]", "tests/api_resources/test_recommend.py::TestAsyncRecommend::test_raw_response_get_recommendations[strict]", "tests/api_resources/test_recommend.py::TestAsyncRecommend::test_streaming_response_get_recommendations[loose]", "tests/api_resources/test_recommend.py::TestAsyncRecommend::test_streaming_response_get_recommendations[strict]", "tests/api_resources/test_recommend.py::TestRecommend::test_method_get_recommendations[loose]", "tests/api_resources/test_recommend.py::TestRecommend::test_method_get_recommendations[strict]", "tests/api_resources/test_recommend.py::TestRecommend::test_method_get_recommendations_with_all_params[loose]", "tests/api_resources/test_recommend.py::TestRecommend::test_method_get_recommendations_with_all_params[strict]", "tests/api_resources/test_recommend.py::TestRecommend::test_raw_response_get_recommendations[loose]", "tests/api_resources/test_recommend.py::TestRecommend::test_raw_response_get_recommendations[strict]", "tests/api_resources/test_recommend.py::TestRecommend::test_streaming_response_get_recommendations[loose]", "tests/api_resources/test_recommend.py::TestRecommend::test_streaming_response_get_recommendations[strict]", "tests/test_client.py::TestAdmesh::test_absolute_request_url[custom http client]", "tests/test_client.py::TestAdmesh::test_absolute_request_url[standard]", "tests/test_client.py::TestAdmesh::test_base_url_env", "tests/test_client.py::TestAdmesh::test_base_url_no_trailing_slash[custom http client]", "tests/test_client.py::TestAdmesh::test_base_url_no_trailing_slash[standard]", "tests/test_client.py::TestAdmesh::test_base_url_setter", "tests/test_client.py::TestAdmesh::test_base_url_trailing_slash[custom http client]", "tests/test_client.py::TestAdmesh::test_base_url_trailing_slash[standard]", "tests/test_client.py::TestAdmesh::test_basic_union_response", "tests/test_client.py::TestAdmesh::test_client_context_manager", "tests/test_client.py::TestAdmesh::test_client_max_retries_validation", "tests/test_client.py::TestAdmesh::test_client_response_validation_error", "tests/test_client.py::TestAdmesh::test_client_timeout_option", "tests/test_client.py::TestAdmesh::test_copied_client_does_not_close_http", "tests/test_client.py::TestAdmesh::test_copy", "tests/test_client.py::TestAdmesh::test_copy_build_request", "tests/test_client.py::TestAdmesh::test_copy_default_headers", "tests/test_client.py::TestAdmesh::test_copy_default_options", "tests/test_client.py::TestAdmesh::test_copy_default_query", "tests/test_client.py::TestAdmesh::test_copy_signature", "tests/test_client.py::TestAdmesh::test_default_headers_option", "tests/test_client.py::TestAdmesh::test_default_query_option", "tests/test_client.py::TestAdmesh::test_http_client_timeout_option", "tests/test_client.py::TestAdmesh::test_invalid_http_client", "tests/test_client.py::TestAdmesh::test_multipart_repeating_array", "tests/test_client.py::TestAdmesh::test_non_application_json_content_type_for_json_data", "tests/test_client.py::TestAdmesh::test_omit_retry_count_header[0]", "tests/test_client.py::TestAdmesh::test_omit_retry_count_header[2]", "tests/test_client.py::TestAdmesh::test_omit_retry_count_header[4]", "tests/test_client.py::TestAdmesh::test_overwrite_retry_count_header[0]", "tests/test_client.py::TestAdmesh::test_overwrite_retry_count_header[2]", "tests/test_client.py::TestAdmesh::test_overwrite_retry_count_header[4]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[-1100--8]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[1--2.0]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[2--1.0]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3--0.5]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3--10-0.5]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-0-0.5]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-20-20]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-60-60]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-61-0.5]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-99999999999999999999999999999999999-0.5]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:26:27 GMT-0.5]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:26:37 GMT-0.5]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:26:57 GMT-20]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:27:37 GMT-60]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:27:38 GMT-0.5]", "tests/test_client.py::TestAdmesh::test_parse_retry_after_header[3-Z<PERSON>, 29 Sep 2023 16:26:27 GMT-0.5]", "tests/test_client.py::TestAdmesh::test_raw_response", "tests/test_client.py::TestAdmesh::test_raw_response_for_binary", "tests/test_client.py::TestAdmesh::test_received_text_for_expected_json", "tests/test_client.py::TestAdmesh::test_request_extra_headers", "tests/test_client.py::TestAdmesh::test_request_extra_json", "tests/test_client.py::TestAdmesh::test_request_extra_query", "tests/test_client.py::TestAdmesh::test_request_timeout", "tests/test_client.py::TestAdmesh::test_retries_taken[exception-0]", "tests/test_client.py::TestAdmesh::test_retries_taken[exception-2]", "tests/test_client.py::TestAdmesh::test_retries_taken[exception-4]", "tests/test_client.py::TestAdmesh::test_retries_taken[status-0]", "tests/test_client.py::TestAdmesh::test_retries_taken[status-2]", "tests/test_client.py::TestAdmesh::test_retries_taken[status-4]", "tests/test_client.py::TestAdmesh::test_retrying_status_errors_doesnt_leak", "tests/test_client.py::TestAdmesh::test_retrying_timeout_errors_doesnt_leak", "tests/test_client.py::TestAdmesh::test_union_response_different_types", "tests/test_client.py::TestAdmesh::test_validate_headers", "tests/test_client.py::TestAsyncAdmesh::test_absolute_request_url[custom http client]", "tests/test_client.py::TestAsyncAdmesh::test_absolute_request_url[standard]", "tests/test_client.py::TestAsyncAdmesh::test_base_url_env", "tests/test_client.py::TestAsyncAdmesh::test_base_url_no_trailing_slash[custom http client]", "tests/test_client.py::TestAsyncAdmesh::test_base_url_no_trailing_slash[standard]", "tests/test_client.py::TestAsyncAdmesh::test_base_url_setter", "tests/test_client.py::TestAsyncAdmesh::test_base_url_trailing_slash[custom http client]", "tests/test_client.py::TestAsyncAdmesh::test_base_url_trailing_slash[standard]", "tests/test_client.py::TestAsyncAdmesh::test_basic_union_response", "tests/test_client.py::TestAsyncAdmesh::test_client_context_manager", "tests/test_client.py::TestAsyncAdmesh::test_client_max_retries_validation", "tests/test_client.py::TestAsyncAdmesh::test_client_response_validation_error", "tests/test_client.py::TestAsyncAdmesh::test_client_timeout_option", "tests/test_client.py::TestAsyncAdmesh::test_copied_client_does_not_close_http", "tests/test_client.py::TestAsyncAdmesh::test_copy", "tests/test_client.py::TestAsyncAdmesh::test_copy_build_request", "tests/test_client.py::TestAsyncAdmesh::test_copy_default_headers", "tests/test_client.py::TestAsyncAdmesh::test_copy_default_options", "tests/test_client.py::TestAsyncAdmesh::test_copy_default_query", "tests/test_client.py::TestAsyncAdmesh::test_copy_signature", "tests/test_client.py::TestAsyncAdmesh::test_default_headers_option", "tests/test_client.py::TestAsyncAdmesh::test_default_query_option", "tests/test_client.py::TestAsyncAdmesh::test_get_platform", "tests/test_client.py::TestAsyncAdmesh::test_http_client_timeout_option", "tests/test_client.py::TestAsyncAdmesh::test_invalid_http_client", "tests/test_client.py::TestAsyncAdmesh::test_multipart_repeating_array", "tests/test_client.py::TestAsyncAdmesh::test_non_application_json_content_type_for_json_data", "tests/test_client.py::TestAsyncAdmesh::test_omit_retry_count_header[0]", "tests/test_client.py::TestAsyncAdmesh::test_omit_retry_count_header[2]", "tests/test_client.py::TestAsyncAdmesh::test_omit_retry_count_header[4]", "tests/test_client.py::TestAsyncAdmesh::test_overwrite_retry_count_header[0]", "tests/test_client.py::TestAsyncAdmesh::test_overwrite_retry_count_header[2]", "tests/test_client.py::TestAsyncAdmesh::test_overwrite_retry_count_header[4]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[-1100--8]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[1--2.0]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[2--1.0]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3--0.5]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3--10-0.5]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-0-0.5]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-20-20]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-60-60]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-61-0.5]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-99999999999999999999999999999999999-0.5]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:26:27 GMT-0.5]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:26:37 GMT-0.5]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:26:57 GMT-20]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:27:37 GMT-60]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-Fri, 29 Sep 2023 16:27:38 GMT-0.5]", "tests/test_client.py::TestAsyncAdmesh::test_parse_retry_after_header[3-Z<PERSON>, 29 Sep 2023 16:26:27 GMT-0.5]", "tests/test_client.py::TestAsyncAdmesh::test_raw_response", "tests/test_client.py::TestAsyncAdmesh::test_raw_response_for_binary", "tests/test_client.py::TestAsyncAdmesh::test_received_text_for_expected_json", "tests/test_client.py::TestAsyncAdmesh::test_request_extra_headers", "tests/test_client.py::TestAsyncAdmesh::test_request_extra_json", "tests/test_client.py::TestAsyncAdmesh::test_request_extra_query", "tests/test_client.py::TestAsyncAdmesh::test_request_timeout", "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[exception-0]", "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[exception-2]", "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[exception-4]", "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[status-0]", "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[status-2]", "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[status-4]", "tests/test_client.py::TestAsyncAdmesh::test_retrying_status_errors_doesnt_leak", "tests/test_client.py::TestAsyncAdmesh::test_retrying_timeout_errors_doesnt_leak", "tests/test_client.py::TestAsyncAdmesh::test_union_response_different_types", "tests/test_client.py::TestAsyncAdmesh::test_validate_headers", "tests/test_deepcopy.py::test_complex_nested_dict", "tests/test_deepcopy.py::test_ignores_other_types", "tests/test_deepcopy.py::test_nested_dict", "tests/test_deepcopy.py::test_nested_list", "tests/test_deepcopy.py::test_simple_dict", "tests/test_deepcopy.py::test_simple_list", "tests/test_extract_files.py::test_ignores_incorrect_paths[array expecting dict]", "tests/test_extract_files.py::test_ignores_incorrect_paths[dict expecting array]", "tests/test_extract_files.py::test_ignores_incorrect_paths[unknown keys]", "tests/test_extract_files.py::test_multiple_files", "tests/test_extract_files.py::test_removes_files_from_input", "tests/test_files.py::test_async_pathlib_includes_file_name", "tests/test_files.py::test_async_supports_anyio_path", "tests/test_files.py::test_async_tuple_input", "tests/test_files.py::test_pathlib_includes_file_name", "tests/test_files.py::test_string_not_allowed", "tests/test_files.py::test_tuple_input", "tests/test_models.py::test_aliases", "tests/test_models.py::test_annotated_types", "tests/test_models.py::test_basic[correct type]", "tests/test_models.py::test_basic[mismatched]", "tests/test_models.py::test_compat_method_no_error_for_warnings", "tests/test_models.py::test_deprecated_alias", "tests/test_models.py::test_dict_of_union", "tests/test_models.py::test_directly_nested_model", "tests/test_models.py::test_discriminated_union_case", "tests/test_models.py::test_discriminated_unions_invalid_data", "tests/test_models.py::test_discriminated_unions_invalid_data_nested_unions", "tests/test_models.py::test_discriminated_unions_invalid_data_uses_cache", "tests/test_models.py::test_discriminated_unions_overlapping_discriminators_invalid_data", "tests/test_models.py::test_discriminated_unions_unknown_variant", "tests/test_models.py::test_discriminated_unions_with_aliases_invalid_data", "tests/test_models.py::test_does_not_coerce_int", "tests/test_models.py::test_double_nested_union", "tests/test_models.py::test_field_named_cls", "tests/test_models.py::test_forwards_compat_model_dump_json_method", "tests/test_models.py::test_forwards_compat_model_dump_method", "tests/test_models.py::test_int_to_float_safe_conversion", "tests/test_models.py::test_iso8601_datetime", "tests/test_models.py::test_list_mismatched_type", "tests/test_models.py::test_list_nested_model", "tests/test_models.py::test_list_of_unions", "tests/test_models.py::test_list_optional_items_nested_model", "tests/test_models.py::test_nested_dictionary_model", "tests/test_models.py::test_nested_union_invalid_data", "tests/test_models.py::test_nested_union_multiple_variants", "tests/test_models.py::test_nested_union_of_mixed_types", "tests/test_models.py::test_nested_union_of_models", "tests/test_models.py::test_omitted_fields", "tests/test_models.py::test_optional_list", "tests/test_models.py::test_optional_list_nested_model", "tests/test_models.py::test_optional_nested_model", "tests/test_models.py::test_raw_dictionary", "tests/test_models.py::test_repr", "tests/test_models.py::test_repr_nested_model", "tests/test_models.py::test_strict_validation_unknown_fields", "tests/test_models.py::test_to_dict", "tests/test_models.py::test_to_json", "tests/test_models.py::test_type_alias_type", "tests/test_models.py::test_type_compat", "tests/test_models.py::test_union_of_dict", "tests/test_models.py::test_union_of_lists", "tests/test_models.py::test_unknown_fields", "tests/test_qs.py::test_array_brackets[class]", "tests/test_qs.py::test_array_brackets[function]", "tests/test_qs.py::test_array_comma[class]", "tests/test_qs.py::test_array_comma[function]", "tests/test_qs.py::test_array_repeat", "tests/test_qs.py::test_basic", "tests/test_qs.py::test_empty", "tests/test_qs.py::test_nested_brackets", "tests/test_qs.py::test_nested_dotted[class]", "tests/test_qs.py::test_nested_dotted[function]", "tests/test_qs.py::test_unknown_array_format", "tests/test_required_args.py::test_keyword_only_param", "tests/test_required_args.py::test_multiple_params", "tests/test_required_args.py::test_multiple_params_multiple_variants", "tests/test_required_args.py::test_multiple_variants", "tests/test_required_args.py::test_positional_param", "tests/test_required_args.py::test_too_many_positional_params", "tests/test_response.py::test_async_response_parse_annotated_type", "tests/test_response.py::test_async_response_parse_bool[FalSe-False]", "tests/test_response.py::test_async_response_parse_bool[False-False]", "tests/test_response.py::test_async_response_parse_bool[TrUe-True]", "tests/test_response.py::test_async_response_parse_bool[True-True]", "tests/test_response.py::test_async_response_parse_bool[false-False]", "tests/test_response.py::test_async_response_parse_bool[true-True]", "tests/test_response.py::test_async_response_parse_custom_model", "tests/test_response.py::test_async_response_parse_custom_stream", "tests/test_response.py::test_async_response_parse_expect_model_union_non_json_content[False]", "tests/test_response.py::test_async_response_parse_mismatched_basemodel", "tests/test_response.py::test_extract_response_type_binary_response", "tests/test_response.py::test_extract_response_type_concrete_subclasses", "tests/test_response.py::test_extract_response_type_direct_class_missing_type_arg", "tests/test_response.py::test_extract_response_type_direct_classes", "tests/test_response.py::test_response_parse_annotated_type", "tests/test_response.py::test_response_parse_bool[FalSe-False]", "tests/test_response.py::test_response_parse_bool[False-False]", "tests/test_response.py::test_response_parse_bool[TrUe-True]", "tests/test_response.py::test_response_parse_bool[True-True]", "tests/test_response.py::test_response_parse_bool[false-False]", "tests/test_response.py::test_response_parse_bool[true-True]", "tests/test_response.py::test_response_parse_custom_model", "tests/test_response.py::test_response_parse_custom_stream", "tests/test_response.py::test_response_parse_expect_model_union_non_json_content[False]", "tests/test_response.py::test_response_parse_mismatched_basemodel", "tests/test_streaming.py::test_basic[async]", "tests/test_streaming.py::test_basic[sync]", "tests/test_streaming.py::test_data_json_escaped_double_new_line[async]", "tests/test_streaming.py::test_data_json_escaped_double_new_line[sync]", "tests/test_streaming.py::test_data_missing_event[async]", "tests/test_streaming.py::test_data_missing_event[sync]", "tests/test_streaming.py::test_event_missing_data[async]", "tests/test_streaming.py::test_event_missing_data[sync]", "tests/test_streaming.py::test_multi_byte_character_multiple_chunks[async]", "tests/test_streaming.py::test_multi_byte_character_multiple_chunks[sync]", "tests/test_streaming.py::test_multiple_data_lines[async]", "tests/test_streaming.py::test_multiple_data_lines[sync]", "tests/test_streaming.py::test_multiple_data_lines_with_empty_line[async]", "tests/test_streaming.py::test_multiple_data_lines_with_empty_line[sync]", "tests/test_streaming.py::test_multiple_events[async]", "tests/test_streaming.py::test_multiple_events[sync]", "tests/test_streaming.py::test_multiple_events_with_data[async]", "tests/test_streaming.py::test_multiple_events_with_data[sync]", "tests/test_streaming.py::test_special_new_line_character[async]", "tests/test_streaming.py::test_special_new_line_character[sync]", "tests/test_transform.py::test_base64_file_input[async]", "tests/test_transform.py::test_base64_file_input[sync]", "tests/test_transform.py::test_datetime_custom_format[async]", "tests/test_transform.py::test_datetime_custom_format[sync]", "tests/test_transform.py::test_datetime_with_alias[async]", "tests/test_transform.py::test_datetime_with_alias[sync]", "tests/test_transform.py::test_dictionary_items[async]", "tests/test_transform.py::test_dictionary_items[sync]", "tests/test_transform.py::test_ignores_invalid_input[async]", "tests/test_transform.py::test_ignores_invalid_input[sync]", "tests/test_transform.py::test_includes_unknown_keys[async]", "tests/test_transform.py::test_includes_unknown_keys[sync]", "tests/test_transform.py::test_iso8601_format[async]", "tests/test_transform.py::test_iso8601_format[sync]", "tests/test_transform.py::test_iterable_of_dictionaries[async]", "tests/test_transform.py::test_iterable_of_dictionaries[sync]", "tests/test_transform.py::test_iterable_union_str[async]", "tests/test_transform.py::test_iterable_union_str[sync]", "tests/test_transform.py::test_list_of_typeddict[async]", "tests/test_transform.py::test_list_of_typeddict[sync]", "tests/test_transform.py::test_nested_list_iso6801_format[async]", "tests/test_transform.py::test_nested_list_iso6801_format[sync]", "tests/test_transform.py::test_optional_iso8601_format[async]", "tests/test_transform.py::test_optional_iso8601_format[sync]", "tests/test_transform.py::test_pydantic_default_field[async]", "tests/test_transform.py::test_pydantic_default_field[sync]", "tests/test_transform.py::test_pydantic_empty_model[async]", "tests/test_transform.py::test_pydantic_empty_model[sync]", "tests/test_transform.py::test_pydantic_mismatched_object_type[async]", "tests/test_transform.py::test_pydantic_mismatched_object_type[sync]", "tests/test_transform.py::test_pydantic_mismatched_types[async]", "tests/test_transform.py::test_pydantic_mismatched_types[sync]", "tests/test_transform.py::test_pydantic_model_to_dictionary[async]", "tests/test_transform.py::test_pydantic_model_to_dictionary[sync]", "tests/test_transform.py::test_pydantic_nested_objects[async]", "tests/test_transform.py::test_pydantic_nested_objects[sync]", "tests/test_transform.py::test_pydantic_unknown_field[async]", "tests/test_transform.py::test_pydantic_unknown_field[sync]", "tests/test_transform.py::test_recursive_typeddict[async]", "tests/test_transform.py::test_recursive_typeddict[sync]", "tests/test_transform.py::test_required_iso8601_format[async]", "tests/test_transform.py::test_required_iso8601_format[sync]", "tests/test_transform.py::test_strips_notgiven[async]", "tests/test_transform.py::test_strips_notgiven[sync]", "tests/test_transform.py::test_top_level_alias[async]", "tests/test_transform.py::test_top_level_alias[sync]", "tests/test_transform.py::test_transform_skipping[async]", "tests/test_transform.py::test_transform_skipping[sync]", "tests/test_transform.py::test_union_datetime[async]", "tests/test_transform.py::test_union_datetime[sync]", "tests/test_transform.py::test_union_of_list[async]", "tests/test_transform.py::test_union_of_list[sync]", "tests/test_transform.py::test_union_of_typeddict[async]", "tests/test_transform.py::test_union_of_typeddict[sync]", "tests/test_utils/test_proxy.py::test_isinstance_does_not_error", "tests/test_utils/test_proxy.py::test_recursive_proxy", "tests/test_utils/test_typing.py::test_extract_type_var", "tests/test_utils/test_typing.py::test_extract_type_var_generic_subclass", "tests/test_utils/test_typing.py::test_extract_type_var_generic_subclass_different_ordering_multiple", "tests/test_utils/test_typing.py::test_extract_type_var_generic_subclass_multiple", "tests/test_utils/test_typing.py::test_extract_type_var_multiple"]