{"tests/test_client.py::TestAdmesh": true, "tests/test_client.py::TestAsyncAdmesh": true, "tests/test_client.py::TestAdmesh::test_copy_build_request": true, "tests/test_client.py::TestAdmesh::test_retries_taken[status-0]": true, "tests/test_client.py::TestAdmesh::test_retries_taken[status-2]": true, "tests/test_client.py::TestAdmesh::test_retries_taken[status-4]": true, "tests/test_client.py::TestAdmesh::test_retries_taken[exception-0]": true, "tests/test_client.py::TestAdmesh::test_retries_taken[exception-2]": true, "tests/test_client.py::TestAdmesh::test_retries_taken[exception-4]": true, "tests/test_client.py::TestAdmesh::test_omit_retry_count_header[0]": true, "tests/test_client.py::TestAdmesh::test_omit_retry_count_header[2]": true, "tests/test_client.py::TestAdmesh::test_omit_retry_count_header[4]": true, "tests/test_client.py::TestAdmesh::test_overwrite_retry_count_header[0]": true, "tests/test_client.py::TestAdmesh::test_overwrite_retry_count_header[2]": true, "tests/test_client.py::TestAdmesh::test_overwrite_retry_count_header[4]": true, "tests/test_client.py::TestAsyncAdmesh::test_copy_build_request": true, "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[status-0]": true, "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[status-2]": true, "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[status-4]": true, "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[exception-0]": true, "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[exception-2]": true, "tests/test_client.py::TestAsyncAdmesh::test_retries_taken[exception-4]": true, "tests/test_client.py::TestAsyncAdmesh::test_omit_retry_count_header[0]": true, "tests/test_client.py::TestAsyncAdmesh::test_omit_retry_count_header[2]": true, "tests/test_client.py::TestAsyncAdmesh::test_omit_retry_count_header[4]": true, "tests/test_client.py::TestAsyncAdmesh::test_overwrite_retry_count_header[0]": true, "tests/test_client.py::TestAsyncAdmesh::test_overwrite_retry_count_header[2]": true, "tests/test_client.py::TestAsyncAdmesh::test_overwrite_retry_count_header[4]": true, "tests/test_client.py::TestAsyncAdmesh::test_get_platform": true}