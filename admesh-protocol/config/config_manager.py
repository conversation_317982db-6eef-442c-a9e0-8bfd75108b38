"""
Configuration manager for AdMesh Protocol
Handles loading and managing environment-specific configurations
"""
import os
import logging
from typing import Dict, Any, Optional
from .base import BaseConfig
from .development import DevelopmentConfig
from .production import ProductionConfig
from .test import TestConfig


logger = logging.getLogger(__name__)


class ConfigManager:
    """Manages configuration loading and access across the application"""

    _instance: Optional['ConfigManager'] = None
    _config: Optional[BaseConfig] = None

    def __new__(cls) -> 'ConfigManager':
        """Singleton pattern to ensure only one config manager instance"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the configuration manager"""
        if self._config is None:
            self._config = self._load_config()
            logger.info(f"Loaded configuration for environment: {self._config.environment}")

    def _load_config(self) -> BaseConfig:
        """Load the appropriate configuration based on environment"""
        environment = BaseConfig.get_environment()

        config_map = {
            "development": DevelopmentConfig,
            "production": ProductionConfig,
            "test": TestConfig
        }

        config_class = config_map.get(environment)
        if not config_class:
            logger.warning(f"Unknown environment '{environment}', defaulting to development")
            config_class = DevelopmentConfig

        try:
            config = config_class()
            logger.info(f"Successfully loaded {config_class.__name__}")
            return config
        except Exception as e:
            logger.error(f"Failed to load configuration for {environment}: {e}")
            # Fallback to development config
            logger.info("Falling back to development configuration")
            return DevelopmentConfig()

    @property
    def config(self) -> BaseConfig:
        """Get the current configuration"""
        if self._config is None:
            self._config = self._load_config()
        return self._config

    def reload_config(self) -> None:
        """Reload the configuration (useful for testing or config changes)"""
        self._config = self._load_config()
        logger.info(f"Configuration reloaded for environment: {self._config.environment}")

    def get_firebase_config(self) -> Dict[str, Any]:
        """Get Firebase configuration"""
        return self.config.firebase_config

    def get_api_base_url(self) -> str:
        """Get API base URL"""
        return self.config.api_base_url

    def get_frontend_url(self) -> str:
        """Get frontend URL"""
        return self.config.frontend_url

    def get_cors_origins(self) -> list:
        """Get CORS origins"""
        return self.config.cors_origins

    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return self.config.database_config

    def is_debug(self) -> bool:
        """Check if debug mode is enabled"""
        return self.config.debug

    def get_log_level(self) -> str:
        """Get logging level"""
        return self.config.log_level

    def get_port(self) -> int:
        """Get server port"""
        return self.config.port

    def get_environment(self) -> str:
        """Get current environment"""
        return self.config.environment

    def get_firebase_credentials_path(self) -> str:
        """Get Firebase credentials path"""
        return self.config.get_firebase_credentials_path()

    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration as a dictionary"""
        return self.config.get_all_config()

    def get_external_services(self) -> Dict[str, Any]:
        """Get external services configuration"""
        if hasattr(self.config, 'external_services'):
            return self.config.external_services
        return {}

    def get_feature_flags(self) -> Dict[str, bool]:
        """Get feature flags"""
        if hasattr(self.config, 'feature_flags'):
            return self.config.feature_flags
        return {}

    def get_security_config(self) -> Dict[str, Any]:
        """Get security configuration"""
        if hasattr(self.config, 'security_config'):
            return self.config.security_config
        return {}

    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance configuration"""
        if hasattr(self.config, 'performance_config'):
            return self.config.performance_config
        return {}


# Global configuration manager instance
config_manager = ConfigManager()


def get_config() -> BaseConfig:
    """Get the current configuration instance"""
    return config_manager.config


def get_firebase_config() -> Dict[str, Any]:
    """Get Firebase configuration"""
    return config_manager.get_firebase_config()


def get_api_base_url() -> str:
    """Get API base URL"""
    return config_manager.get_api_base_url()


def get_frontend_url() -> str:
    """Get frontend URL"""
    return config_manager.get_frontend_url()


def get_cors_origins() -> list:
    """Get CORS origins"""
    return config_manager.get_cors_origins()


def is_debug() -> bool:
    """Check if debug mode is enabled"""
    return config_manager.is_debug()


def get_environment() -> str:
    """Get current environment"""
    return config_manager.get_environment()
