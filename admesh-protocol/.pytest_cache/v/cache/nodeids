["tests/api/routes/test_leaderboard.py::test_create_custom_leaderboard", "tests/api/routes/test_leaderboard.py::test_delete_custom_leaderboard", "tests/api/routes/test_leaderboard.py::test_get_custom_leaderboard", "tests/api/routes/test_leaderboard.py::test_get_leaderboard", "tests/api/routes/test_leaderboard.py::test_list_custom_leaderboards", "tests/api/routes/test_leaderboard.py::test_update_custom_leaderboard", "tests/test_admin_endpoints.py::test_admin_update_role", "tests/test_admin_endpoints.py::test_get_user_by_email", "tests/test_admin_endpoints.py::test_set_admin_status", "tests/test_admin_endpoints.py::test_update_role", "tests/test_agent_endpoints.py::test_agent_conversions_endpoint", "tests/test_agent_endpoints.py::test_agent_queries_endpoint", "tests/test_agent_endpoints.py::test_agent_stats_endpoint", "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_email_domain_mismatch", "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_invalid_website_url", "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_missing_company_name", "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_missing_website", "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_success", "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_www_domain_handling", "tests/test_build_admesh_link.py::test_build_admesh_link_all_params", "tests/test_build_admesh_link.py::test_build_admesh_link_development", "tests/test_build_admesh_link.py::test_build_admesh_link_explicit_test", "tests/test_build_admesh_link.py::test_build_admesh_link_minimal_params", "tests/test_build_admesh_link.py::test_build_admesh_link_production", "tests/test_offers.py::test_create_offer", "tests/test_semantic_matching.py::TestSemanticMatching::test_cosine_similarity_identical_vectors", "tests/test_semantic_matching.py::TestSemanticMatching::test_cosine_similarity_opposite_vectors", "tests/test_semantic_matching.py::TestSemanticMatching::test_cosine_similarity_orthogonal_vectors", "tests/test_semantic_matching.py::TestSemanticMatching::test_cosine_similarity_range", "tests/test_semantic_matching.py::TestSemanticMatching::test_cosine_similarity_similar_vectors", "tests/test_semantic_matching.py::TestSemanticMatching::test_cosine_similarity_zero_vector", "tests/test_semantic_matching.py::TestSemanticMatching::test_embed_text_failure", "tests/test_semantic_matching.py::TestSemanticMatching::test_embed_text_success", "tests/test_signup_agent.py::test_detect_signup_fields", "tests/test_subscription_limits.py::test_check_offer_limit_at_limit", "tests/test_subscription_limits.py::test_check_offer_limit_under_limit", "tests/test_subscription_limits.py::test_check_offer_limit_unlimited", "tests/test_subscription_limits.py::test_offer_activation_with_limits", "tests/test_subscription_limits.py::test_update_offer_count"]