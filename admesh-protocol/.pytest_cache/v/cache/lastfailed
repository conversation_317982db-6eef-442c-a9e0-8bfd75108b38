{"tests/api/routes/test_leaderboard.py::test_create_custom_leaderboard": true, "tests/api/routes/test_leaderboard.py::test_update_custom_leaderboard": true, "tests/api/routes/test_leaderboard.py::test_list_custom_leaderboards": true, "tests/api/routes/test_leaderboard.py::test_delete_custom_leaderboard": true, "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_success": true, "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_missing_website": true, "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_missing_company_name": true, "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_email_domain_mismatch": true, "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_invalid_website_url": true, "tests/test_brand_registration.py::TestBrandRegistration::test_brand_registration_www_domain_handling": true, "tests/test_offers.py::test_create_offer": true, "tests/test_signup_agent.py::test_detect_signup_fields": true}