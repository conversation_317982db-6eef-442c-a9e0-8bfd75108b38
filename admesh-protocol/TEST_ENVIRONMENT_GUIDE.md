# AdMesh Protocol Test Environment Guide

This guide explains how to use the test environment for testing production builds locally with the local API server.

## 🎯 What is the Test Environment?

The test environment allows you to:
- Run production-like builds locally
- Connect to your local API server (`http://127.0.0.1:8000`)
- Test with production-like settings but safe test data
- Debug production issues without affecting live systems

## 🚀 Quick Start

### Switch to Test Environment
```bash
# Method 1: Using the switch script
python scripts/switch_env.py test

# Method 2: Using the run script (switches and starts server)
./run_test.sh

# Method 3: Manual copy
cp .env.test .env
```

### Start the Server
```bash
# Start the test server (production-like settings)
python -m uvicorn api.main:app --host 0.0.0.0 --port 8000

# For development with auto-reload (if needed)
python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000
```

## 📋 Environment Comparison

| Environment | API URL | Firebase Project | Debug Mode | Use Case |
|-------------|---------|------------------|------------|----------|
| **Development** | `http://127.0.0.1:8000` | `admesh-dev` | ✅ True | Local development |
| **Test** | `http://127.0.0.1:8000` | `admesh-dev` | ❌ False | Production build testing |
| **Production** | `https://api.useadmesh.com` | `admesh-9560c` | ❌ False | Live deployment |

## 🔧 Configuration Details

### Test Environment Features:
- **API URL**: `http://127.0.0.1:8000` (local server)
- **Firebase**: Uses development project (`admesh-dev`)
- **Debug**: Disabled (production-like)
- **Logging**: INFO level
- **Feature Flags**: Enabled (production-like)
- **Stripe**: Test mode keys
- **CORS**: Limited to localhost origins

### Environment Files:
- `.env.dev` - Development environment
- `.env.test` - Test environment (what you want!)
- `.env.production` - Production deployment
- `.env` - Active environment (copied from above)

## 🛠️ Available Commands

```bash
# Environment switching
python scripts/switch_env.py dev     # Development
python scripts/switch_env.py test    # Test (local production)
python scripts/switch_env.py prod    # Production

# Quick start scripts
./run_dev.sh                         # Development server
./run_test.sh                        # Test server
./run_prod.sh                        # Production server

# Validation
python scripts/validate_environment.py
```

## 🔍 Verification

To verify you're in the test environment:

```bash
# Check environment variables
grep "ENV=" .env

# Check configuration loading
python -c "
from dotenv import load_dotenv
load_dotenv()
from config.config_manager import get_environment, get_config
print(f'Environment: {get_environment()}')
print(f'API URL: {get_config().api_base_url}')
print(f'Debug: {get_config().debug}')
"
```

Expected output:
```
Environment: test
API URL: http://127.0.0.1:8000
Debug: False
```

## 🎯 Your Use Case

When you want to run production builds locally but connect to your local API:

```bash
# Backend (admesh-protocol)
python scripts/switch_env.py test
python -m uvicorn api.main:app --host 0.0.0.0 --port 8000

# Frontend (admesh-dashboard) 
npm run env:test && npm run build && npm run start
```

This gives you:
- ✅ Production build settings
- ✅ Local API connection (`http://127.0.0.1:8000`)
- ✅ Test data and safe environment
- ✅ Production-like feature flags and logging
