# Development Environment Configuration
# Environment
ENV=development
DEBUG=true
LOG_LEVEL=DEBUG
PORT=8000

# Firebase Configuration
GOOGLE_APPLICATION_CREDENTIALS=./firebase/dev-serviceAccountKey.json

# API Configuration
SITE_URL=http://127.0.0.1:8000

# External Services
OPENROUTER_API_KEY=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
RESEND_API_KEY=re_EqBocs67_QFgdUbbUEkVvGeommYFp2wXQ

# Test IDs and Keys
NEXT_PUBLIC_AGENT_ID=ZAinjf9SuPYCk7u8r0ZEeKY2fV42
NEXT_PUBLIC_USER_ID=C8SMDERS3naFG2k103EDBacoTCy2
NEXT_PUBLIC_AGENT_API_KEY=sk_test_IFTLcrkWf2Hx9GUfb6pNXwaMpJ4GryRw

# Stripe Configuration (Test)
STRIPE_SECRET_KEY=sk_test_51RCBjAH6mzmcFCnlJb1U2mIHuOwg4VlMUrfQQOXFJAYnW3V2vP3vFTw8TwssxFe0TLkxZhrj7GUaAWQP1r1gSqYk00O8wwwGVQ
STRIPE_DOMAIN=http://localhost:3000
STRIPE_WEBHOOK_SECRET=whsec_9fcb1b9eaf6db496cd90a0afec2ce995a8ac3915e2a312466e5a40f2f9beb614

# Frontend URLs
FRONTEND_URL=http://localhost:3000
FRONTEND_URL_PROD=https://useadmesh.com

# Security
FERNET_SECRET=VlWiQCfcL7LW34MpCN9UCmWHU0F2cUQecgiQBtGeCTM=

# Feature Flags (Development)
ENABLE_ANALYTICS=true
ENABLE_RATE_LIMITING=false
ENABLE_CACHING=false
ENABLE_EMAIL_VERIFICATION=false
ENABLE_TRUST_SCORE_THROTTLING=false
